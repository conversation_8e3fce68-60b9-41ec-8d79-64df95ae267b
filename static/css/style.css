body {
    font-family: '<PERSON><PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding: 20px;
}

.header {
    margin-bottom: 20px;
}

.header h1 {
    font-size: 24px;
    font-weight: bold;
    color: #333;
    margin: 0;
}

/* Beautiful User Info and Authentication Styling */
.user-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
}

.user-info i {
    margin-right: 6px;
}

.logout-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 12px;
    margin-left: 10px;
    transition: all 0.2s;
    text-decoration: none;
}

.logout-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
    transform: translateY(-1px);
    text-decoration: none;
}

/* Enhanced GitHub Link Styling */
.github-link {
    color: #495057;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 20px;
    border: 1px solid #dee2e6;
    background-color: white;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.github-link:hover {
    color: #0d6efd;
    background-color: #e9ecef;
    border-color: #0d6efd;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.github-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

/* Enhanced Navigation Tab Styling */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 1.5rem;
}

.nav-tabs .nav-link {
    color: #495057;
    font-weight: 500;
    padding: 12px 20px;
    border: none;
    border-radius: 8px 8px 0 0;
    margin-right: 4px;
    transition: all 0.2s ease;
    background: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.nav-tabs .nav-link:hover {
    color: #0d6efd;
    background: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    font-weight: 600;
    background: white;
    border-bottom: 3px solid #0d6efd;
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.15);
}

/* Coming Soon Badge Styling */
.coming-soon-badge {
    font-size: 0.65rem;
    margin-left: 5px;
    padding: 0.25em 0.5em;
    vertical-align: middle;
    font-weight: normal;
    background: linear-gradient(135deg, #ffc107, #fd7e14);
    color: white;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

/* Enhanced Card Styling */
.card {
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 1px solid #dee2e6;
    padding: 16px 24px;
    border-radius: 12px 12px 0 0;
}

.card-header h3 {
    font-size: 18px;
    margin: 0;
    color: #333;
    font-weight: 600;
}

.main-content {
    height: calc(100vh - 100px);
}

.stream-panel, .control-panel {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card {
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 12px 20px;
}

.card-header h3 {
    font-size: 18px;
    margin: 0;
    color: #333;
}

.card-body {
    padding: 20px;
    flex: 1;
    overflow: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
}

#stream-container {
    height: 100%;
    width: 100%;
    background-color: #eee;
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

#stream-placeholder {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #666;
    text-align: center;
    padding: 20px;
}

#stream-frame {
    width: 100%;
    height: 100%;
    border: none;
}

#log-container, #agentcore-log-container {
    flex: 1;
    min-height: 400px; /* Reduced height since it's now inside the controls card */
    overflow-y: auto;
    background-color: #212529;
    border-radius: 4px;
    padding: 10px;
    font-family: monospace;
    color: #f8f9fa;
    width: 100%; /* Ensure full width */
    text-align: left !important;
}

/* Ensure the logs div inside log-container is also left-aligned */
#logs, #agentcore-logs {
    text-align: left !important;
    width: 100%;
    display: block;
}

#logs .log-entry, #agentcore-logs .log-entry {
    margin-bottom: 5px;
    word-break: break-word;
    text-align: left !important;
    line-height: 1.4;
    display: block !important;
    white-space: pre-wrap;
    padding: 0 !important;
    margin-left: 0 !important;
    text-indent: 0 !important;
    justify-content: flex-start !important;
    align-items: flex-start !important;
}

.log-timestamp {
    color: #adb5bd;
    margin-right: 8px;
}

.log-stdout {
    color: #8bc34a;
}

.log-stderr {
    color: #ff5252;
}

.log-info {
    color: #2196f3;
}

.log-error {
    color: #ff5252;
}

.log-success {
    color: #4caf50;
}

/* Ensure badges in log entries are properly aligned */
#logs .log-entry .badge, #agentcore-logs .log-entry .badge {
    margin-right: 8px !important;
    margin-left: 0 !important;
    vertical-align: baseline !important;
    display: inline !important;
}

/* Ensure timestamps are consistently formatted */
#logs .log-entry .text-muted, #agentcore-logs .log-entry .text-muted {
    margin-right: 8px !important;
    margin-left: 0 !important;
    display: inline !important;
}

/* Override any Bootstrap flexbox or alignment on log entry children */
#logs .log-entry *, #agentcore-logs .log-entry * {
    text-align: left !important;
}

.control-panel .card {
    flex: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
}

.logs-section {
    border-top: 1px solid #dee2e6;
    padding-top: 15px;
    display: block !important;
    text-align: left !important;
    align-items: flex-start !important;
}

/* Modern Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 10px 18px;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: none;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Primary button (Run Task) */
.btn-info {
    background: linear-gradient(135deg, #2196f3, #0d6efd);
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #1976d2, #0b5ed7);
    color: white;
}

/* Danger button (Stop Sandbox) */
.btn-danger {
    background: linear-gradient(135deg, #ff5252, #dc3545);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #f44336, #c82333);
    color: white;
}

/* Success button */
.btn-success {
    background: linear-gradient(135deg, #4caf50, #28a745);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #43a047, #218838);
    color: white;
}

/* Secondary buttons */
.btn-outline-secondary {
    border: 1px solid #ced4da;
    background: white;
    color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #f8f9fa;
    color: #495057;
    border-color: #adb5bd;
}

.btn-outline-secondary.active {
    background: linear-gradient(135deg, #6c757d, #5a6268);
    color: white;
}

/* Primary outline button */
.btn-outline-primary {
    border: 1px solid #0d6efd;
    background: white;
    color: #0d6efd;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #2196f3, #0d6efd);
    color: white;
    border-color: #0d6efd;
}

/* Badge styles */
.badge {
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Fullscreen modal styles */
.modal-fullscreen {
    width: 100vw;
    max-width: none;
    height: 100vh;
    margin: 0;
}

.modal-fullscreen .modal-content {
    height: 100vh;
    border: none;
    border-radius: 0;
}

.modal-fullscreen .modal-header {
    border-bottom: 1px solid #495057;
    padding: 0.75rem 1rem;
    min-height: 60px;
}

.modal-fullscreen .modal-body {
    overflow: hidden;
}

#agentcore-fullscreen-container {
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

#agentcore-fullscreen-container:empty::before {
    content: "Loading browser content...";
    color: #fff;
    font-size: 18px;
    text-align: center;
}

#agentcore-fullscreen-container iframe {
    width: 100% !important;
    height: 100% !important;
    border: none !important;
    border-radius: 0 !important;
}

/* Fullscreen button styles */
#agentcore-fullscreen-btn {
    transition: all 0.2s ease;
}

#agentcore-fullscreen-btn:hover {
    background-color: #0d6efd;
    color: white;
    border-color: #0d6efd;
}

@media (max-width: 767.98px) {
    .main-content {
        height: auto;
    }
    
    .stream-panel, .control-panel {
        height: auto;
        margin-bottom: 20px;
    }
    
    #stream-container {
        height: 400px;
    }
    
    #log-container, #agentcore-log-container {
        height: 500px; /* Smaller height on mobile but still larger than original */
    }
}
