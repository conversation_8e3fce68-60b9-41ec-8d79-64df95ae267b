# Python virtual environments
venv/
env/
ENV/
.venv/
.env/
.python-version

# Environment variables
.env
.env.local
.env.development
.env.test
.env.production

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Jupyter Notebook
.ipynb_checkpoints

# AWS
.aws/
.aws-sam/

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Playwright
node_modules/
/test-results/
/playwright-report/
/playwright/.cache/

login_history.txt

.kiro/

amazon-bedrock-agentcore-samples/
