for(let e=1;e<=3;e++)try{self.importScripts(`${baseUrl}/lib/lz4/lz4_decoder.js`);break}catch(t){self.postMessage({type:"warn",message:`[LZ4-Worker] Attempt ${e} of 3 to import lz4_decoder has failed: ${t.message}`})}"undefined"==typeof Module&&(self.postMessage({type:"error",message:"[LZ4-Worker] Could not import lz4_decoder so lz4decoder-worker will be shut down"}),self.close()),self.frameCount=0,self.totalFrameDecodingTime=0,self.onmessage=function(e){switch(e.data.type){case"frame":let t,a,r,s,o,l,f,n=(new Date).getTime(),i=e.data.frame.byteLength,d=new Uint8Array(e.data.frame);if(e.data.frame=null,e.data.clips){let n;const m=new Uint32Array(e.data.clips),u=m.length>>2;e.data.clips=null,l=new Uint8Array(e.data.output),e.data.output=null,a=e.data.maxSize,r=Module._malloc(i+a),o=new Uint8Array(Module.HEAPU8.buffer,r,a),s=new Uint8Array(Module.HEAPU8.buffer,r+a,i),s.set(new Uint8Array(d.buffer)),d=null;for(let e=0;e<u;e++){const t=m[4*e],s=m[4*e+1],o=m[4*e+2];if(n=m[4*e+3],f=_LZ4_decompress_safe(r+a+t,r+o,s,n),f!==n){self.postMessage({type:"info",message:`[LZ4-Worker] Error in worker: decodedSize (${f} is different than expected one (${n})`});break}}Module._free(r),t=(new Date).getTime(),f==n&&l.set(o),o=null,self.postMessage({type:"frame",buffer:l.buffer,ok:f==n},[l.buffer])}else{let n=e.data.width,m=e.data.height;if(a=e.data.isRgb?3*n*m:n*(m+m/2),r=Module._malloc(i+a),o=new Uint8Array(Module.HEAPU8.buffer,r,a),s=new Uint8Array(Module.HEAPU8.buffer,r+a,i),s.set(new Uint8Array(d.buffer)),d=null,f=_LZ4_decompress_safe(r+a,r,i,a),Module._free(r),t=(new Date).getTime(),f<0)self.postMessage({type:"info",message:"[LZ4-Worker] Error in worker: decodedSize ("+f+") must be positive!!!"}),o=null;else{if(e.data.usingWebGLDecoder)l=new Uint8Array(o);else{l=new Uint8ClampedArray(n*m*4);for(let e=0,t=0;t<f;t+=3)l[e]=o[t],l[e+1]=o[t+1],l[e+2]=o[t+2],l[e+3]=255,e+=4}o=null,self.postMessage({type:"frame",buffer:l,width:n,height:m,x:e.data.x,y:e.data.y,decodedSize:f},[l.buffer])}}self.frameCount++,self.totalFrameDecodingTime+=t-n;break;case"stats":self.postMessage({type:"stats",frameCount:self.frameCount,totalFrameDecodingTime:self.totalFrameDecodingTime,averageDecodingTime:self.totalFrameDecodingTime/self.frameCount,averageDecodingFPS:self.frameCount/(self.totalFrameDecodingTime/1e3)}),self.frameCount=0,self.totalFrameDecodingTime=0;break;case"close":self.postMessage({type:"info",message:"Closing LZ4 worker"}),self.close()}};