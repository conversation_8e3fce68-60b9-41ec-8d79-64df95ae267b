self.importScripts(baseUrl+"/lib/jsmpeg/jsmpeg.min.js"),self.frameCount=0,self.totalFrameDecodingTime=0,self.decoder=new JSMpeg.Decoder.MPEG1Video({}),self.decoder.connect(new class{resize(e,t){this.width=e,this.height=t}render(e,t,s){let a=new Uint8Array(e.byteLength+t.byteLength+s.byteLength);a.set(e),a.set(s,e.byteLength),a.set(t,e.byteLength+t.byteLength),self.postMessage({type:"frame",buffer:a,width:this.width,height:this.height},[a.buffer])}}),self.onmessage=function(e){switch(e.data.type){case"frame":const t=(new Date).getTime();self.decoder.write(0,new Uint8Array(e.data.frame)),self.decoder.decode(),self.frameCount++,self.totalFrameDecodingTime+=(new Date).getTime()-t;break;case"stats":postMessage({type:"stats",frameCount:self.frameCount,totalFrameDecodingTime:self.totalFrameDecodingTime,averageDecodingTime:self.totalFrameDecodingTime/self.frameCount,averageDecodingFPS:self.frameCount/(self.totalFrameDecodingTime/1e3)}),self.frameCount=0,self.totalFrameDecodingTime=0;break;case"close":self.postMessage({type:"info",message:"Closing MPEG1 worker"}),self.close()}};