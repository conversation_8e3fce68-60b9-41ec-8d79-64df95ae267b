self.importScripts(baseUrl+"/lib/broadway/Decoder.js"),self.frameCount=0,self.totalFrameDecodingTime=0,self.broadway=new Decoder,self.broadway.onPictureDecoded=function(e,a,t){let s;s=new Uint8Array(e.length),s.set(e,0,e.length),self.postMessage({type:"frame",buffer:s,width:a,height:t},[s.buffer])},self.onmessage=function(e){switch(e.data.type){case"frame":const a=(new Date).getTime();broadway.decode(new Uint8Array(e.data.frame)),self.frameCount++,self.totalFrameDecodingTime+=(new Date).getTime()-a;break;case"stats":self.postMessage({type:"stats",frameCount:self.frameCount,totalFrameDecodingTime:self.totalFrameDecodingTime,averageDecodingTime:self.totalFrameDecodingTime/self.frameCount,averageDecodingFPS:self.frameCount/(self.totalFrameDecodingTime/1e3)}),self.frameCount=0,self.totalFrameDecodingTime=0;break;case"close":self.postMessage({type:"info",message:"Closing H264 worker"}),self.close()}};