# Amazon DCV Web Client SDK

The Amazon DCV Web Client SDK is a JavaScript library that lets you
develop your own Amazon DCV web browser client application.
Users of your application will be able to connect and interact with
a native Amazon DCV session.

## Prerequisites

Before you start working with the Amazon DCV Web Client SDK, ensure
that you're familiar with Amazon DCV and Amazon DCV sessions. For more
information, see the [Amazon DCV Administrator Guide](https://docs.aws.amazon.com/dcv/latest/adminguide).

The Amazon DCV Web Client SDK supports Amazon DCV server version 2020
and later.

## Browser Support

The Amazon DCV Web Client SDK supports JavaScript (ES6) and it can
be used from JavaScript or TypeScript applications.

The Amazon DCV Web Client SDK supports the following web browsers:
 * Google Chrome - Latest three major versions
 * Mozilla Firefox - Latest three major versions
 * Microsoft Edge - Latest three major versions
 * Apple Safari for macOS - Latest three major versions

Even if the SDK is designed to work with latest browsers versions,
it can be transpiled to ES5 code and used with older browser. In
this case some features or functionalities may be not available.

## Versioning

The Amazon DCV Web Client SDK follows the semantic versioning model.
Version has the following format: major.minor.patch. A change in
the major version, such as from 1.x.x to 2.x.x, indicates that breaking
changes that might require code changes and a planned deployment
have been introduced. A change in the minor version, such as from
1.1.x to 1.2.x, is backwards compatible, but might include
deprecated elements.

## How to use the library

The SDK reference, and some examples are available in the
[official documentation website](https://docs.aws.amazon.com/dcv/latest/websdkguide).

## License

For the license see the file EULA.txt.
For the Third party notices see the file third-party-licenses.txt.
