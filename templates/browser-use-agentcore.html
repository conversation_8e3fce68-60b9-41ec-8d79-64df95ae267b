<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browser Use - Agentcore BrowserTool - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }
        .user-info {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .user-info i {
            margin-right: 6px;
        }
        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: 1px solid rgba(255,255,255,0.3);
            color: white;
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 12px;
            margin-left: 10px;
            transition: all 0.2s;
        }
        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
            color: white;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <!-- Header -->
        <div class="row header">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cube me-2"></i>Sandbox on AWS Demo UI</h1>
                <div class="user-info">
                    <i class="fas fa-user"></i>
                    Welcome, {{ user.username }}!
                    <a href="/logout" class="logout-btn text-decoration-none">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="row main-content">
            <div class="col-12 mt-4">
                <!-- Modern, smaller sub-tabs for Browser Use -->
                <div class="d-flex justify-content-center mb-4">
                    <div class="btn-group" role="group" aria-label="Browser Use Options">
                        <a href="/browser-use" class="btn btn-sm btn-outline-primary" id="e2b-tab">E2B Desktop</a>
                        <a href="/browser-use-agentcore" class="btn btn-sm btn-primary active" id="agentcore-tab" aria-current="page">Agentcore BrowserTool</a>
                    </div>
                </div>

                <!-- Content for Agentcore BrowserTool tab -->
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="agentcore-content">
                        <div class="row main-content">
                    <!-- Left panel: Browser stream -->
                    <div class="col-md-7 stream-panel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h3>Browser Stream - Powered by Agentcore BrowserTool</h3>
                                <div class="d-flex align-items-center">
                                    <button id="agentcore-fullscreen-btn" class="btn btn-outline-primary btn-sm me-2" title="Open browser in fullscreen mode" data-bs-toggle="tooltip" data-bs-placement="bottom" style="display: none;">
                                        <i class="fas fa-expand"></i>
                                    </button>
                                    <div id="agentcore-controls" class="d-flex align-items-center" style="display: none;">
                                        <span id="agentcore-timer" class="badge bg-secondary me-2 py-2 px-3 fs-6">00:00</span>
                                        <span id="agentcore-session-id" class="badge bg-info me-2"></span>
                                        <button id="stop-agentcore-browser" class="btn btn-danger py-1 px-2 fs-6">Stop</button>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="agentcore-browser-container" style="width: 100%; height: 500px; position: relative;">
                                    <div id="agentcore-browser-placeholder" style="display: flex; align-items: center; justify-content: center; height: 100%; background-color: #f8f9fa; border: 2px dashed #dee2e6; border-radius: 8px;">
                                        <p style="color: #6c757d; margin: 0;">No active browser session. Start a new session to begin.</p>
                                    </div>
                                    <iframe id="agentcore-browser-frame" style="display: none; width: 100%; height: 100%; border: none; border-radius: 8px;"></iframe>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right panel: Controls and logs -->
                    <div class="col-md-5 control-panel">
                        <div class="card">
                            <div class="card-header">
                                <h3>Agentcore Controls</h3>
                            </div>
                            <div class="card-body">
                                <form id="agentcore-task-form">
                                    <div class="mb-3">
                                        <label for="agentcore-task-input" class="form-label">Browser Task Prompt:</label>
                                        <textarea id="agentcore-task-input" class="form-control" rows="4" placeholder="Enter your browser automation task here..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <label class="form-label">Example Browser Tasks:</label>
                                        <div class="d-flex flex-wrap gap-2 mb-2">
                                            <button type="button" class="btn btn-sm btn-outline-secondary agentcore-example-task" data-prompt="Navigate to Google and search for 'latest AI developments'">Google Search</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary agentcore-example-task" data-prompt="Go to Amazon and search for 'wireless headphones' and show me the top 3 results">Amazon Search</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary agentcore-example-task" data-prompt="Visit Wikipedia and find information about machine learning">Wikipedia Research</button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary agentcore-example-task" data-prompt="Navigate to GitHub and search for popular Python repositories">GitHub Browse</button>
                                        </div>
                                    </div>

                                    <div class="d-flex flex-wrap gap-2 mb-4">
                                        <button type="button" id="run-agentcore-task" class="btn btn-sm btn-info">Run Browser Task</button>
                                    </div>

                                    <div class="logs-section mt-4">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h4>Browser Logs</h4>
                                            <button id="clear-agentcore-logs" class="btn btn-sm btn-outline-secondary">Clear</button>
                                        </div>
                                        <div id="agentcore-log-container">
                                            <div id="agentcore-logs"></div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/script.js"></script>
    <script src="/static/js/agentcore-browser.js"></script>

    <!-- Agentcore Browser Fullscreen Modal -->
    <div class="modal fade" id="agentcoreFullscreenModal" tabindex="-1" aria-labelledby="agentcoreFullscreenModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-fullscreen">
            <div class="modal-content">
                <div class="modal-header bg-dark text-white">
                    <h5 class="modal-title" id="agentcoreFullscreenModalLabel">
                        <i class="fas fa-globe me-2"></i>Agentcore Browser - Fullscreen View
                    </h5>
                    <button type="button" class="btn btn-outline-light btn-sm" data-bs-dismiss="modal" aria-label="Close">
                        <i class="fas fa-times me-1"></i>Exit Fullscreen
                    </button>
                </div>
                <div class="modal-body p-0">
                    <div id="agentcore-fullscreen-container" style="width: 100%; height: calc(100vh - 60px);">
                        <!-- Browser iframe will be moved here when in fullscreen mode -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Enlarged Image">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
