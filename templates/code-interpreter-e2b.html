<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Code Interpreter on E2B - Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/theme/monokai.min.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .CodeMirror {
            height: 300px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .output-container {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 0 0 4px 4px;
            padding: 15px;
            min-height: 150px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        #event-log {
            max-height: 150px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.85rem;
        }
        .execution-results .result-section {
            margin-bottom: 15px;
        }
        .execution-results pre {
            margin: 0;
            white-space: pre-wrap;
        }
        .code-controls {
            margin: 15px 0;
        }
        .sandbox-info {
            font-size: 0.8rem;
            color: #6c757d;
            margin-top: 5px;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }

        /* GitHub link styles */
        .github-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease-in-out;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .github-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #0d6efd;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .github-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cube me-2"></i>Sandbox on AWS Demo UI</h1>
                <div class="d-flex align-items-center gap-3">
                    <a href="https://github.com/teaguexiao/sandbox-on-aws-demo" target="_blank" rel="noopener noreferrer" class="github-link">
                        <svg class="github-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                        </svg>
                        View Source
                    </a>
                    {% if user %}
                    <div class="user-info">
                        <i class="fas fa-user"></i>
                        Welcome, {{ user.username }}!
                        <a href="/logout" class="logout-btn text-decoration-none">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="row main-content">
            <div class="col-12 mt-4">
                <!-- Modern, smaller sub-tabs for Code Interpreter -->
                <div class="d-flex justify-content-center mb-4">
                    <div class="btn-group" role="group" aria-label="Code Interpreter Options">
                        <a href="/code-interpreter" class="btn btn-sm btn-outline-primary" id="lambda-tab">Lambda</a>
                        <a href="/code-interpreter-e2b" class="btn btn-sm btn-primary active" id="e2b-tab" aria-current="page">E2B</a>
                        <a href="/code-interpreter-agentcore" class="btn btn-sm btn-outline-primary" id="agentcore-tab">AgentCore</a>
                        <a href="/code-interpreter-ec2" class="btn btn-sm btn-outline-primary" id="ec2-tab">EC2</a>
                    </div>
                </div>
                
                <!-- Content for E2B tab -->
                <div class="tab-content">
                    <div class="tab-pane fade show active" id="e2b-content">
                        <div class="container">
                            <h2 class="text-center mb-4">E2B Code Interpreter</h2>
                            <div class="row">
                                <div class="col-12">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <span>Python Code</span>
                                                <div>
                                                    <span class="sandbox-info" id="sandbox-status">Sandbox ID: Initializing...</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <textarea id="code-editor" class="form-control" rows="10">
# Example: Compute prime numbers using Sieve of Eratosthenes
# 示例：使用埃拉托斯特尼筛法计算质数
import numpy as np
import time
from tqdm import tqdm

def sieve_of_eratosthenes(limit):
    sieve = [True] * (limit + 1)
    sieve[0] = sieve[1] = False
    
    for i in tqdm(range(2, int(np.sqrt(limit)) + 1), desc="Finding primes"):
        if sieve[i]:
            for j in range(i*i, limit + 1, i):
                sieve[j] = False
        time.sleep(0.01)  # Add delay to extend runtime
    
    return [i for i in range(limit + 1) if sieve[i]]

# Perform calculation with progress tracking
print("Starting prime number calculation...")
start_time = time.time()

# Calculate primes up to 10000
primes = sieve_of_eratosthenes(10000)

# Generate statistics about the primes
prime_count = len(primes)
avg_prime = np.mean(primes)
max_gaps = []

for i in range(1, len(primes)):
    max_gaps.append(primes[i] - primes[i-1])

execution_time = time.time() - start_time
print(f"\nExecution completed in {execution_time:.2f} seconds")
print(f"\nPrimes: {primes}")
</textarea>
                                            <div class="code-controls d-flex justify-content-between">
                                                <button id="execute-btn" class="btn btn-primary">Execute Code</button>
                                                <button id="reset-btn" class="btn btn-secondary">Reset Sandbox</button>
                                            </div>
                                            <div class="mt-4">
                                                <h5>Output:</h5>
                                                <div id="execution-log" class="mb-2">
                                                    <div class="card">
                                                        <div class="card-header bg-secondary text-white">
                                                            <h6 class="mb-0">Execution Log</h6>
                                                        </div>
                                                        <div class="card-body p-2">
                                                            <div id="event-log" class="small">Waiting for execution...</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="card">
                                                    <div class="card-header bg-dark text-white">
                                                        <h6 class="mb-0">Results</h6>
                                                    </div>
                                                    <div class="card-body p-0">
                                                        <div id="code-output" class="output-container">Results will appear here...</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/python/python.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize CodeMirror
            const codeEditor = CodeMirror.fromTextArea(document.getElementById('code-editor'), {
                mode: 'python',
                theme: 'monokai',
                lineNumbers: true,
                indentUnit: 4,
                autoCloseBrackets: true,
                matchBrackets: true
            });

            // Get elements
            const executeBtn = document.getElementById('execute-btn');
            const resetBtn = document.getElementById('reset-btn');
            const codeOutput = document.getElementById('code-output');
            const eventLog = document.getElementById('event-log');
            const sandboxStatus = document.getElementById('sandbox-status');
            
            let currentSandboxId = null;
            
            // Function to add event to log with timestamp
            function logEvent(message, type = 'info') {
                const now = new Date();
                const timestamp = now.toLocaleTimeString();
                const eventClass = type === 'error' ? 'text-danger' : 
                                   type === 'success' ? 'text-success' : 
                                   type === 'warning' ? 'text-warning' : 'text-info';
                
                const eventElement = document.createElement('div');
                eventElement.className = eventClass;
                eventElement.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
                eventLog.appendChild(eventElement);
                eventLog.scrollTop = eventLog.scrollHeight;
            }

            // Execute code function
            executeBtn.addEventListener('click', async function() {
                const code = codeEditor.getValue();
                if (!code.trim()) {
                    codeOutput.textContent = 'Please enter some code to execute.';
                    return;
                }

                // Show loading state
                executeBtn.disabled = true;
                executeBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Executing...';
                codeOutput.textContent = 'Executing code...';
                logEvent('Starting code execution...', 'info');

                try {
                    const response = await fetch('/api/e2b/execute', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ code: code })
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        logEvent('Code execution completed', 'success');
                        
                        // Format the output in a structured way
                        if (result.output && typeof result.output === 'string') {
                            // Parse the execution results from the string
                            try {
                                // Extract stdout and stderr from the output
                                const outputData = JSON.parse(result.output.replace('Execution(Results: [], Logs: Logs(stdout: ', '{"stdout":').replace('), stderr: [', ', "stderr":[').replace(']), Error: None)', '}'));
                                
                                // Create a structured output
                                let formattedOutput = '<div class="execution-results">';
                                
                                // Add stdout content
                                if (outputData.stdout && outputData.stdout.length > 0) {
                                    formattedOutput += '<div class="result-section"><h6 class="text-success">Standard Output:</h6>';
                                    formattedOutput += '<pre class="p-2 bg-light border rounded">';
                                    outputData.stdout.forEach(line => {
                                        formattedOutput += line.replace(/\n/g, '<br>');
                                    });
                                    formattedOutput += '</pre></div>';
                                }
                                
                                // Add stderr content if it exists and isn't empty
                                if (outputData.stderr && outputData.stderr.length > 0) {
                                    formattedOutput += '<div class="result-section mt-3"><h6 class="text-warning">Progress Output:</h6>';
                                    formattedOutput += '<pre class="p-2 bg-light border rounded">';
                                    outputData.stderr.forEach(line => {
                                        formattedOutput += line.replace(/\n/g, '<br>');
                                    });
                                    formattedOutput += '</pre></div>';
                                }
                                
                                formattedOutput += '</div>';
                                codeOutput.innerHTML = formattedOutput;
                            } catch (e) {
                                // Fallback if parsing fails
                                codeOutput.textContent = result.output;
                                logEvent('Could not parse structured output', 'warning');
                            }
                        } else {
                            codeOutput.textContent = result.output || 'No output returned';
                        }
                        
                        currentSandboxId = result.sandbox_id;
                        sandboxStatus.textContent = `Sandbox ID: ${currentSandboxId}`;
                        logEvent(`Results displayed from sandbox: ${currentSandboxId}`, 'info');
                    } else {
                        codeOutput.textContent = `Error: ${result.error}`;
                        logEvent(`Execution error: ${result.error}`, 'error');
                    }
                } catch (error) {
                    codeOutput.textContent = `Error: ${error.message}`;
                } finally {
                    // Reset button state
                    executeBtn.disabled = false;
                    executeBtn.textContent = 'Execute Code';
                }
            });

            // Reset sandbox function
            resetBtn.addEventListener('click', async function() {
                // Show loading state
                resetBtn.disabled = true;
                resetBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Resetting...';
                logEvent('Resetting sandbox...', 'info');
                
                try {
                    const response = await fetch('/api/e2b/reset', {
                        method: 'POST'
                    });

                    const result = await response.json();
                    
                    if (result.success) {
                        currentSandboxId = result.sandbox_id;
                        sandboxStatus.textContent = `Sandbox ID: ${currentSandboxId}`;
                        codeOutput.innerHTML = '<div class="alert alert-success">Sandbox reset successfully. Ready for new code execution.</div>';
                        logEvent(`New sandbox created with ID: ${currentSandboxId}`, 'success');
                        // Clear event log when resetting sandbox
                        eventLog.innerHTML = '';
                        logEvent('Sandbox initialized and ready', 'info');
                    } else {
                        codeOutput.innerHTML = `<div class="alert alert-danger">Error resetting sandbox: ${result.error}</div>`;
                        logEvent(`Failed to reset sandbox: ${result.error}`, 'error');
                    }
                } catch (error) {
                    codeOutput.textContent = `Error: ${error.message}`;
                } finally {
                    // Reset button state
                    resetBtn.disabled = false;
                    resetBtn.textContent = 'Reset Sandbox';
                }
            });

            // Initialize sandbox on page load
            resetBtn.click();
        });
    </script>
    <script src="/static/js/script.js"></script>
    
    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Enlarged Image">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
