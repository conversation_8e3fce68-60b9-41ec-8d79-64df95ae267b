<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sandbox on AWS Demo UI</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="/static/css/style.css">
    <style>
        .nav-tabs .nav-link {
            color: #495057;
            font-weight: 500;
        }
        .nav-tabs .nav-link.active {
            color: #0d6efd;
            font-weight: 600;
        }
        .coming-soon-badge {
            font-size: 0.65rem;
            margin-left: 5px;
            padding: 0.25em 0.5em;
            vertical-align: middle;
            font-weight: normal;
        }
        .hero-section {
            background-color: #f8f9fa;
            padding: 3rem 0;
            border-radius: 8px;
            margin-bottom: 2rem;
        }
        .feature-card {
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            height: 100%;
            transition: transform 0.3s ease;
        }
        .feature-card:hover {
            transform: translateY(-5px);
        }
        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: #0d6efd;
        }
        .architecture-img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .tech-badge {
            background-color: #e9ecef;
            color: #495057;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        /* Enhanced image interaction styles */
        .architecture-img {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
        }
        
        .architecture-img:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }
        
        #modalImage {
            max-height: 85vh;
            object-fit: contain;
            width: auto;
            max-width: 100%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: transform 0.3s ease;
        }
        
        .modal-xl {
            max-width: 90%;
        }
        
        .modal-content {
            background-color: #f8f9fa;
            border: none;
            border-radius: 8px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            border-radius: 8px 8px 0 0;
        }
        
        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            background-color: #ffffff;
            border-radius: 0 0 8px 8px;
        }
        
        .modal-body {
            padding: 20px;
            background-color: #ffffff;
        }

        /* GitHub link styles */
        .github-link {
            color: #333;
            text-decoration: none;
            font-weight: 500;
            padding: 8px 16px;
            border-radius: 6px;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            transition: all 0.2s ease-in-out;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .github-link:hover {
            color: #0d6efd;
            background-color: #e9ecef;
            border-color: #0d6efd;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .github-icon {
            width: 20px;
            height: 20px;
            fill: currentColor;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row header">
            <div class="col-12 d-flex justify-content-between align-items-center">
                <h1><i class="fas fa-cube me-2"></i>Sandbox on AWS Demo UI</h1>
                <div class="d-flex align-items-center gap-3">
                    <a href="https://github.com/teaguexiao/sandbox-on-aws-demo" target="_blank" rel="noopener noreferrer" class="github-link">
                        <svg class="github-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 0C5.374 0 0 5.373 0 12 0 17.302 3.438 21.8 8.207 23.387c.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23A11.509 11.509 0 0112 5.803c1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.30.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576C20.566 21.797 24 17.3 24 12c0-6.627-5.373-12-12-12z"/>
                        </svg>
                        View Source
                    </a>
                    {% if user %}
                    <div class="user-info">
                        <i class="fas fa-user"></i>
                        Welcome, {{ user.username }}!
                        <a href="/logout" class="logout-btn text-decoration-none">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Navigation Tabs -->
        <div class="row mb-3">
            <div class="col-12">
                <ul class="nav nav-tabs" id="sandboxTabs">
                    <li class="nav-item">
                        <a class="nav-link active" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/sandbox-lifecycle">Sandbox Lifecycle</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/browser-use">Browser Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/code-interpreter">Code Interpreter</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/computer-use">Computer Use</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-search">AI Search <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/ai-ppt">AI PPT <span class="badge bg-warning text-dark coming-soon-badge">Coming Soon</span></a>
                    </li>
                </ul>
            </div>
        </div>
        
        <!-- Hero Section -->
        <div class="row hero-section">
            <div class="col-md-8 offset-md-2 text-center">
                <h2 class="display-4 mb-4">Sandbox on AWS</h2>
                <p class="lead mb-4">A powerful solution <strong>purpose-built for agentic workloads</strong>, providing secure, scalable, and flexible AI agent environments using E2B sandbox, AWS Lambda, and AWS Fargate.</p>
                <div class="d-flex justify-content-center flex-wrap">
                    <span class="badge tech-badge p-2">E2B Sandbox</span>
                    <span class="badge tech-badge p-2">AWS Lambda</span>
                    <span class="badge tech-badge p-2">AWS Fargate</span>
                    <span class="badge tech-badge p-2">Claude 3.7/4 Sonnet</span>
                </div>
            </div>
        </div>
        
        <!-- Agentic Workload Section -->
        <div class="row mb-5">
            <div class="col-md-10 offset-md-1">
                <div class="alert alert-primary" role="alert">
                    <h4 class="alert-heading">Optimized for Agentic Workloads</h4>
                    <p>Sandbox on AWS is specifically designed to handle the unique requirements of AI agents. Unlike traditional computing environments, our solution provides the perfect balance of freedom and control that autonomous agents need to perform complex tasks while maintaining security and reliability.</p>
                    <hr>
                    <p class="mb-0">Whether your agents need to browse the web, use desktop applications, write code, or create presentations, our infrastructure provides the ideal foundation for all agentic scenarios.</p>
                </div>
            </div>
        </div>
        
        <!-- Features Section -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h3>Key Benefits</h3>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card feature-card p-4">
                    <div class="card-body text-center">
                        <div class="feature-icon">🔒</div>
                        <h4 class="card-title">Secure Execution</h4>
                        <p class="card-text">Run AI agents in isolated sandboxes with controlled access to resources, ensuring security and preventing unauthorized actions.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card feature-card p-4">
                    <div class="card-body text-center">
                        <div class="feature-icon">⚡</div>
                        <h4 class="card-title">Scalable Infrastructure</h4>
                        <p class="card-text">Leverage AWS Lambda and Fargate to automatically scale resources based on demand, handling multiple concurrent agent tasks efficiently.</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4 mb-4">
                <div class="card feature-card p-4">
                    <div class="card-body text-center">
                        <div class="feature-icon">🧩</div>
                        <h4 class="card-title">Flexible Use Cases</h4>
                        <p class="card-text">Support diverse scenarios from browser automation to coding assistance, search, and presentation creation with a unified infrastructure.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Architecture Section -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h3>Architecture Overview</h3>
            </div>
            
            <div class="col-md-10 offset-md-1 text-center">
                <div class="row mb-4">
                    <div class="col-12">
                        <img src="/static/images/sandbox-on-aws-architecutre-diagram.png" alt="Sandbox on AWS Architecture Diagram" class="architecture-img mb-3">
                        <p class="text-muted">The architecture leverages E2B sandboxes for secure execution environments, AWS Lambda for serverless compute, and AWS Fargate for containerized workloads.</p>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <h4 class="mb-3">E2B Data Flow</h4>
                        <img src="/static/images/e2b_infra_data_flow_simplify.jpg" alt="E2B Infrastructure Data Flow" class="architecture-img mb-3">
                        <p class="text-muted">This diagram illustrates how data flows through the E2B infrastructure, helping developers understand the technical benefits of this approach.</p>
                    </div>
                    <div class="col-md-6">
                        <h4 class="mb-3">Lambda Sandbox Workflow</h4>
                        <img src="/static/images/lambda_code_interpreter.png" alt="Lambda Sandbox Workflow" class="architecture-img mb-3">
                        <p class="text-muted">This diagram shows the workflow of the Lambda sandbox code interpreter implementation.</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Use Cases Section -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h3>Supported Scenarios</h3>
            </div>
            
            <div class="col-md-6 offset-md-3">
                <div class="list-group">
                    <a href="/browser-use" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        Browser Use
                        <span class="badge bg-primary rounded-pill">Active</span>
                    </a>
                    <a href="/code-interpreter" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        Code Interpreter
                        <span class="badge bg-primary rounded-pill">Active</span>
                    </a>
                    <a href="/sandbox-lifecycle" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        Sandbox Lifecycle
                        <span class="badge bg-primary rounded-pill">Active</span>
                    </a>
                    <a href="/computer-use" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        Computer Use
                        <span class="badge bg-secondary rounded-pill">Coming Soon</span>
                    </a>
                    <a href="/ai-search" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        AI Search
                        <span class="badge bg-secondary rounded-pill">Coming Soon</span>
                    </a>
                    <a href="/ai-ppt" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                        AI PPT
                        <span class="badge bg-secondary rounded-pill">Coming Soon</span>
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Agent Capabilities -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h3>Agent Capabilities</h3>
            </div>
            
            <div class="col-md-10 offset-md-1">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Autonomous Operation</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li>Self-directed task execution without human intervention</li>
                                    <li>Dynamic decision-making based on environment feedback</li>
                                    <li>Error recovery and alternative path exploration</li>
                                    <li>Long-running task persistence across sessions</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Tool Interaction</h5>
                            </div>
                            <div class="card-body">
                                <ul>
                                    <li>Web browsing with full DOM manipulation</li>
                                    <li>File system operations for data processing</li>
                                    <li>Application control for complex workflows</li>
                                    <li>API integration for external service access</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Technical Details -->
        <div class="row mb-5">
            <div class="col-12 text-center mb-4">
                <h3>Technical Details</h3>
            </div>
            
            <div class="col-md-10 offset-md-1">
                <div class="card">
                    <div class="card-body">
                        <h5>Core Technologies</h5>
                        <ul>
                            <li><strong>E2B Sandbox:</strong> Secure, isolated environments for running agent tasks</li>
                            <li><strong>AWS Lambda:</strong> Serverless compute for handling API requests and lightweight processing</li>
                            <li><strong>AWS Fargate:</strong> Container orchestration for more complex, resource-intensive workloads</li>
                            <li><strong>Claude 3.7/4 Sonnet:</strong> Advanced AI model for intelligent agent capabilities</li>
                        </ul>
                        
                        <h5 class="mt-4">Key Features</h5>
                        <ul>
                            <li>Isolated execution environments for security</li>
                            <li>Auto-scaling based on demand</li>
                            <li>Real-time desktop streaming</li>
                            <li>Comprehensive logging and monitoring</li>
                            <li>Multi-scenario support with unified infrastructure</li>
                            <li><strong>Agent-friendly APIs and interfaces</strong></li>
                            <li><strong>Stateful agent execution across sessions</strong></li>
                            <li><strong>Tool-use capabilities for autonomous agents</strong></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/script.js"></script>
    
    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Image Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Enlarged Image">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
